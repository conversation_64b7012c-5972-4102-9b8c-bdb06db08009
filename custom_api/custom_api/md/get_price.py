from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, project_root)

from custom_api.utils.get_market_data import fetch_latest_price_from_bars


def get_price(symbol, type_=None, source=None, fields=None):
    """
    获取最新行情

    :param symbol: str 合约唯一代码
    :param type_: str 数据类型，默认None，使用ODM_DEPTH
    :param source: str 行情来源，默认None，使用MT4
    :param fields: list 指定返回对象字段，默认None，表示全部返回
    :return: list 行情对象列表

    返回格式:
    [{
        'status': '1',  # 价格状态 1-正常, 2-异常
        'source': 'MT4',  # 数据渠道
        'type': 'ODM_DEPTH',  # 数据类型
        'symbol': 'EURUSD',  # 合约代码
        'time': 1598922000100,  # 时间戳
        'best_bid': 1.19907,  # 最优买价
        'best_bid_amt': 94,  # 最优买价数量
        'best_ask': 1.19919,  # 最优卖价
        'best_ask_amt': 94,  # 最优卖价数量
        'asks': [1.19919, 1.19925, ...],  # 卖出报盘价格
        'ask_vols': [94, 94, ...],  # 卖出报盘数量
        'bids': [1.19907, 1.19901, ...],  # 买入报盘价格
        'bid_vols': [94, 94, ...],  # 买入报盘数量
        'limit_up': '',  # 涨停价
        'limit_down': ''  # 跌停价
    }]
    """

    try:
        # 调用底层接口获取实时价格数据
        latest_bar = fetch_latest_price_from_bars(symbol, source)

        if latest_bar is None:
            return []

        # 定义所有可能的字段
        all_fields = [
            'status', 'source', 'type', 'symbol', 'time',
            'best_bid', 'best_bid_amt', 'best_ask', 'best_ask_amt',
            'asks', 'ask_vols', 'bids', 'bid_vols',
            'limit_up', 'limit_down'
        ]

        # 处理字段筛选
        if fields is not None:
            # 只保留用户指定的字段，但必须在all_fields范围内
            allowed_fields = [field for field in fields if field in all_fields]
        else:
            # 如果没有指定字段，返回所有字段
            allowed_fields = all_fields

        # 过滤数据字段
        filtered_data_list = []

        # 只保留允许的字段
        filtered_data = {}
        for field in allowed_fields:
            if field in latest_bar:
                filtered_data[field] = latest_bar[field]
            else:
                # 为缺失的字段设置默认值
                if field in ['status']:
                    filtered_data[field] = '1'  # 默认正常状态
                elif field in ['source']:
                    filtered_data[field] = source
                elif field in ['type']:
                    filtered_data[field] = type_
                elif field in ['symbol']:
                    # todo 最有卖价最优卖价等价格
                    filtered_data[field] = ''
        filtered_data_list.append(filtered_data)

        return filtered_data_list

    except Exception as e:
        print(f"获取价格数据失败: {e}")
        return []


if __name__ == '__main__':
    # 测试代码
    print("测试获取EURUSD价格:")
    price_data = get_price("EURUSD")
    print(price_data)

    print("\n测试获取指定字段:")
    price_data_filtered = get_price("EURUSD", fields=['symbol', 'best_bid', 'best_ask', 'time'])
    print(price_data_filtered)
